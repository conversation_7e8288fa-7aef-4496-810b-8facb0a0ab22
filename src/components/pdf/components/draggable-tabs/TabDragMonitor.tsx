import {usePdfStore} from '@/store/pdf-store';
import React, {useEffect, useRef} from 'react';
import {useDragLayer} from 'react-dnd';
import {DragItem, DragTypes} from './types';
import useDragTab from "@/components/pdf/components/draggable-tabs/hooks/drag-tab.ts";
import {CloseOutlined} from "@ant-design/icons";

export const TabDragMonitor = ({}) => {
    const createdWindowIdRef = useRef<string | null>(null);
    const {
        createWindow,
        updateWindow,
        windows,
        closeWindow,
        setDragTabWindowId,
        setDragTabSourceWindowId
    } = usePdfStore((state) => ({
        createWindow: state.createWindow,
        updateWindow: state.updateWindow,
        windows: state.windows,
        closeWindow: state.closeWindow,
        setDragTabWindowId: state.setDragTabWindowId,
        setDragTabSourceWindowId: state.setDragTabSourceWindowId,
    }));

    const {
        isDragging,
        item,
        currentOffset,
    } = useDragLayer((monitor) => ({
        isDragging: monitor.isDragging(),
        item: monitor.getItem() as DragItem,
        currentOffset: monitor.getSourceClientOffset(),
    }));

    const {checkIfOutsideContainer} = useDragTab()

    useEffect(() => {
        if (!isDragging || !currentOffset || !item || item.type !== DragTypes.TAB) {
            console.log("TabDragMonitor reset", isDragging, currentOffset, item)
            // 重置状态
            createdWindowIdRef.current = null;
            setDragTabWindowId("")
            return;
        }

        const sourceWindowId = item.windowId
        const sourceWindow = windows.get(sourceWindowId)
        const isFromMainTabBar = sourceWindowId === 'main';

        // 检查是否脱离了标签栏区域
        const isOutsideTabBar = checkIfOutsideContainer(item, currentOffset)
        console.log("TabDragMonitor isOutsideTabBar", isOutsideTabBar)

        // 对于非主窗口，检查是否只有一个标签
        const isSingleTabWindow = sourceWindow && sourceWindow.tabs.length === 1;

        // 计算窗口位置
        const windowPosition = {
            x: currentOffset.x,
            y: currentOffset.y + 49
        };
        // 决定拖拽行为
        if (!isFromMainTabBar && isSingleTabWindow) {
            createdWindowIdRef.current = sourceWindowId
            updateWindow(createdWindowIdRef.current!, {position: windowPosition, zIndex: Date.now()});
            setDragTabWindowId(sourceWindowId)
        } else if (isOutsideTabBar && !createdWindowIdRef.current) {
            const windowId = createWindow([item.tabItem], windowPosition);
            createdWindowIdRef.current = windowId;
            setDragTabWindowId(windowId)
            console.log('Created window for dragged tab:', windowId);
        } else if (createdWindowIdRef.current && currentOffset) {
            updateWindow(createdWindowIdRef.current!, {position: windowPosition});
        }
    }, [isDragging, item?.id, currentOffset?.x, currentOffset?.y]);

    if (!isDragging || !currentOffset || !item || item.type !== DragTypes.TAB) {
        return null
    }
    console.log("TabDragMonitor render")
    if (checkIfOutsideContainer(item, currentOffset)) {
        return (
            <div className="bg-gray-50 border-b border border-gray-200 rounded-tl-lg rounded-tr-lg" style={{
                position: 'fixed',
                pointerEvents: 'none',
                zIndex: Date.now(),
                left: currentOffset.x,
                top: currentOffset.y,
                width: 800,
                height: '50px',
            }}>
                <div className="flex items-center min-w-0 flex-1">
                    <div className="group">
                        <div className="
                            group relative flex items-center px-3 py-2 cursor-pointer select-none
                            border-r border-gray-200 min-w-0 max-w-48
                            transition-all duration-200 ease-in-out
                            bg-white border-b-2 border-b-blue-500 text-blue-600">
                            <div className="flex items-center min-w-0 flex-1"><span
                                className="truncate text-sm font-medium">{item.tabItem.label}</span></div>
                            <button className="
                                    ml-2 p-1 rounded-full group-hover:opacity-100
                                    hover:bg-gray-200 transition-opacity duration-200
                                    opacity-70 hover:opacity-100
                                  ">
                                <CloseOutlined className="text-xs"/>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    } else {

    }

};
