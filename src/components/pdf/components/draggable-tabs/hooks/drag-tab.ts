import {useCallback} from "react";
import {usePdfStore} from "@/store/pdf-store.ts";
import {DragItem, DropResult} from "@/components/pdf/components/draggable-tabs/types.ts";

const useDragTab = () => {
    const {
        windows,
        setTabItems,
        setActiveAid,
        removeTabFromWindow,
        updateWindow,
        closeWindow,
        addTabToWindow,
        setDragTabWindowId,
    } = usePdfStore((state) => ({
        windows: state.windows,
        setTabItems: state.setTabItems,
        setActiveAid: state.setActiveAid,
        removeTabFromWindow: state.removeTabFromWindow,
        updateWindow: state.updateWindow,
        closeWindow: state.closeWindow,
        addTabToWindow: state.addTabToWindow,
        setDragTabWindowId: state.setDragTabWindowId,
    }));
    // 检查是否脱离了容器区域
    const checkIfOutsideContainer = useCallback((item: DragItem, clientOffset: {
        x: number;
        y: number
    } | null) => {
        // const state = usePdfStore.getState()
        // const dragTabWindowId = state.dragTabWindowId
        // const dragTabId = state.windows.get(dragTabWindowId)?.tabs[0].key
        if (!item.containerRect || !clientOffset) {
            return false
        }
        // if (item.tabItem.key === dragTabId) {
        //     return true
        // }
        const containerRect = item.containerRect;

        return (
            clientOffset.x + (item.width ?? 0) < containerRect.left ||
            clientOffset.x > containerRect.left + containerRect.width ||
            clientOffset.y + (item.height ?? 0) < containerRect.top ||
            clientOffset.y > containerRect.top + containerRect.height

        )
    }, []);
    // 查找标签所属窗口
    const getTabWindowId = useCallback((item: DragItem) => {
        const currentTabItems = usePdfStore.getState().tabItems || [];
        const isFromMainTabBar = currentTabItems.some(tabItem => tabItem?.key === item.id);

        // 查找标签来自哪个窗口
        let sourceWindowId = "main"
        if (!isFromMainTabBar) {
            for (const [windowId, windowState] of windows) {
                if (windowState.tabs.some(tab => tab.key === item.id)) {
                    sourceWindowId = windowId;
                    break;
                }
            }
        }
        return sourceWindowId;
    }, [windows])
    const dragEnd = useCallback((item: DragItem, result: DropResult | null) => {
        const dragTabWindowId = usePdfStore.getState().dragTabWindowId
        if (dragTabWindowId) {
            const dragWindow = usePdfStore.getState().windows.get(dragTabWindowId)
            if (dragWindow) {
                updateWindow(dragTabWindowId, {
                    position: {
                        x: dragWindow.position.x,
                        y: dragWindow.position.y - 49
                    },
                    size: {
                        width: 800,
                        height: 600
                    }
                });
            }
        }
        const sourceWindowId = item.windowId
        console.log("DraggableTab dragEnd", dragTabWindowId, sourceWindowId)
        // if (sourceWindowId === 'main') {
        //     console.log("DraggableTab remove tab window-main")
        //     // 从主标签页移除
        //     const currentTabItems = usePdfStore.getState().tabItems || [];
        //     const newTabItems = currentTabItems.filter((tabItem) => tabItem?.key !== item.id);
        //     setTabItems(newTabItems);
        //
        //     // 更新活动标签
        //     const activeAid = usePdfStore.getState().activeAid;
        //     if (activeAid === item.id) {
        //         const index = currentTabItems.findIndex((tabItem) => tabItem.key === item.id)
        //         if (newTabItems.length > 0) {
        //             setActiveAid(newTabItems[index === currentTabItems.length - 1 ? index - 1 : index].key)
        //         } else {
        //             setActiveAid("")
        //         }
        //     }
        // } else {
        //     console.log("DraggableTab remove tab", sourceWindowId, usePdfStore.getState().windows.get(sourceWindowId)?.tabs)
        //     // 从源窗口移除标签
        //     removeTabFromWindow(sourceWindowId, item.id);
        // }
        if (!result) {
            return
        }

        if (result.type === 'merge') {
            console.log('DragTab addTabToWindow', result.targetContainer)
            if (result.targetContainer === 'main') {
                // 添加到主标签页
                const currentTabItems = usePdfStore.getState().tabItems || [];
                const newTabItems = [...currentTabItems, item.tabItem];
                setTabItems(newTabItems);

                // 设置为活动标签
                setActiveAid(item.id);
            } else {
                addTabToWindow(result.targetContainer!, item.tabItem)
            }
        }
        closeWindow(dragTabWindowId)
    }, [updateWindow, removeTabFromWindow, setTabItems, setActiveAid, getTabWindowId, closeWindow, addTabToWindow])
    return {
        checkIfOutsideContainer,
        getTabWindowId,
        dragEnd,
    }
}

export default useDragTab;